import React, { useState, useEffect, useRef } from 'react';
import { YTHForm, YTHList } from 'yth-ui';
import { message, Input, Button, Spin } from 'antd';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import moment from 'moment';
import traceApi from '@/service/traceApi';
import type { ApiResponse, TraceRecord } from '@/service/traceApi';
import type { Form } from '@formily/core/esm/models';

type PropsTypes = {
  dataObj: TraceRecord;
  closeModal: () => void;
  /** handle:处理、view:查看 */
  modalType: 'handle' | 'view';
};

/**
 * @description 溯源记录 查看、处理弹窗
 * @returns
 */
const TraceabilityModal: React.FC<PropsTypes> = ({ dataObj, closeModal = () => {}, modalType }) => {
  const { TextArea } = Input;
  const [isLoading] = useState<boolean>(false);
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  // YTHList 相关状态
  const listActionRef = useRef<ActionType>();
  const listAction: ActionType = YTHList.createAction();

  // 列表列配置
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'alarmId',
      title: '报警ID',
      width: 120,
      display: true,
    },
    {
      dataIndex: 'highRiskEnterpriseName',
      title: '高风险企业名称',
      width: 180,
      display: true,
    },
    {
      dataIndex: 'lowRiskEnterpriseName',
      title: '低风险企业名称',
      width: 180,
      display: true,
    },
    {
      dataIndex: 'pollutionTypeText',
      title: '污染类型',
      width: 120,
      display: true,
      render: (value: string, record: TraceRecord) => {
        return record.pollutionTypeText || '-';
      },
    },
    {
      dataIndex: 'tracingCode',
      title: '溯源指标',
      width: 120,
      display: true,
    },
    {
      dataIndex: 'pollutantConcentration',
      title: '相关报警值',
      width: 120,
      display: true,
    },
    {
      dataIndex: 'tracingTime',
      title: '溯源时间',
      width: 160,
      display: true,
      render: (value: string) => {
        return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
  ];

  useEffect(() => {
    if (dataObj && dataObj.id && dataObj.id !== '') {
      form.setValues({
        ...dataObj,
        id: dataObj.id,
        handleTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      });
    } else {
      message.error('数据出错').then(() => {
        closeModal();
      });
    }
  }, [dataObj, form, closeModal]);

  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  return (
    <div>
      <Spin spinning={isLoading}>
        <YTHForm form={form}>
          <YTHForm.Item
            name="alarmId"
            title="报警ID"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="highRiskEnterpriseName"
            title="高风险企业名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="lowRiskEnterpriseName"
            title="低风险企业名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="pollutionType"
            title="污染类型"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="tracingCode"
            title="溯源指标"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="pollutantConcentration"
            title="相关报警值"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="tracingTime"
            title="溯源时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              disabled: true,
              placeholder: '',
              precision: 'second',
              formatter: 'YYYY-MM-DD HH:mm:ss',
            }}
          />

          {modalType === 'handle' && (
            <>
              <YTHForm.Item
                name="handlePsn"
                title="处理人"
                labelType={1}
                required
                componentName="Input"
                componentProps={{
                  placeholder: '请输入处理人',
                }}
              />
              <YTHForm.Item
                name="handleTime"
                title="处理时间"
                labelType={1}
                required
                componentName="DatePicker"
                componentProps={{
                  disabled: true,
                  placeholder: '',
                  precision: 'second',
                  formatter: 'YYYY-MM-DD HH:mm:ss',
                }}
              />
              <YTHForm.Item
                name="handleContent"
                title="处理内容"
                mergeRow={2}
                required
                component={TextArea}
                componentProps={{
                  placeholder: '请输入处理内容',
                  rows: 4,
                }}
              />
              <YTHForm.Item
                name="handleResult"
                title="处理结果"
                mergeRow={2}
                required
                component={TextArea}
                componentProps={{
                  placeholder: '请输入处理结果',
                  rows: 3,
                }}
              />
            </>
          )}
        </YTHForm>

        {/* 底部添加YTHList组件 */}
        <div style={{ marginTop: '20px' }}>
          <h3 style={{ marginBottom: '16px' }}>相关溯源记录</h3>
          <YTHList
            defaultQuery={{}}
            code="traceabilityModalList"
            action={listAction}
            actionRef={listActionRef}
            showRowSelection={false}
            operation={[]}
            listKey="id"
            extraOperation={[]}
            request={async (filter, pagination) => {
              try {
                // 查询所有溯源记录
                const resData: ApiResponse<TraceRecord[]> = await traceApi.queryTraceRecordList({
                  aescs: [],
                  descs: [],
                  condition: filter || {},
                  currentPage: pagination.current,
                  pageSize: pagination.pageSize,
                });

                if (resData.code && resData.code === 200) {
                  resData.data.forEach((_item, index) => {
                    (resData.data[index] as TraceRecord & { serialNo?: number }).serialNo =
                      (pagination.current - 1) * pagination.pageSize + index + 1;
                  });
                  return {
                    data: resData.data,
                    total: resData.total,
                    success: true,
                  };
                }
                return {
                  data: [],
                  total: 0,
                  success: false,
                };
              } catch {
                // 查询溯源记录失败，返回空数据
                return {
                  data: [],
                  total: 0,
                  success: false,
                };
              }
            }}
            rowOperationWidth={70}
            rowOperation={(row) => {
              return [
                {
                  element: (
                    <Button
                      type="link"
                      size="small"
                      onClick={() => {
                        // 可以在这里添加查看详情的逻辑
                        message.info(`查看记录: ${row.alarmId}`);
                      }}
                    >
                      查看
                    </Button>
                  ),
                },
              ];
            }}
            columns={columns}
            pagination={{
              pageSize: 5, // 设置较小的分页大小
              showSizeChanger: false,
            }}
          />
        </div>

        <div style={{ marginTop: '20px', textAlign: 'right' }}>
          <Button onClick={cancel}>取消</Button>
        </div>
      </Spin>
    </div>
  );
};

export default TraceabilityModal;
