import { envRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

/**
 * 分页查询参数接口
 */
interface PageQueryParams {
  aescs?: string[];
  descs?: string[];
  condition: Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 溯源记录数据接口
 */
export interface TraceRecord {
  id: string; // 主键
  alarmId: string; // 报警id
  highRiskEnterpriseId: string; // 高风险企业id
  highRiskEnterpriseName: string; // 高风险企业名称
  lowRiskEnterpriseId: string; // 低风险企业id
  lowRiskEnterpriseName: string; // 低风险企业名称
  pollutantConcentration: number; // 相关报警值
  pollutionType: string; // 污染类型
  tracingCode: string; // 溯源指标
  tracingTime: string; // 溯源时间
  pollutionTypeText?: string; // 污染类型文本
}

/**
 * 环境管理报警信息数据接口
 */
export interface AlarmInfo {
  id: string; // 主键
  alarmLevel: string; // 报警级别
  alarmTime: string; // 报警时间
  currVal: number; // 当前值
  description: string; // 监测对象
  disposeContent: string; // 处置内容
  disposePsn: string; // 处置人
  disposeState: string; // 处置状态 1-已处置
  disposeTm: string; // 处置时间
  equipCd: string; // 设备编号
  equipNm: string; // 设备名称
  equipState: string; // 监测设备状态 0-离线 1-在线
  firstLevelMax: number; // 一级阈值上限
  firstLevelMin: number; // 一级阈值下限
  frequency: string; // 采集频率
  indexCd: string; // 监测指标编码
  indexNm: string; // 监测指标名称
  measureUnit: string; // 计量单位
  orgCd: string; // 单位编码
  orgNm: string; // 单位名称
  secondLevelMax: number; // 二级阈值上限
  secondLevelMin: number; // 二级阈值下限
  supplyUnit: string; // 所属单位
  type: string; // 设备监测类型
}

/**
 * 溯源 API 接口定义
 */
interface TraceApiInterface {
  /** 查询溯源记录列表 */
  queryTraceRecordList: (data: PageQueryParams) => Promise<ApiResponse<TraceRecord[]>>;
  /** 查询环境管理报警信息列表 */
  queryAlarmInfoList: (data: PageQueryParams) => Promise<ApiResponse<AlarmInfo[]>>;
}

/**
 * 溯源模块 API
 */
const traceApi: TraceApiInterface = {
  /** 查询溯源记录列表 */
  queryTraceRecordList: (data: PageQueryParams) => {
    return envRequest.post('/pollutionTracing/page', { data });
  },

  /** 处理溯源记录 */
  handleTraceRecord: (data: TraceHandleParams) => {
    return envRequest.post('/trace/handle', { data });
  },

  /** 查询溯源记录详情 */
  queryTraceRecordDetail: (id: string) => {
    return envRequest.get(`/trace/detail/${id}`);
  },

  /** 生成溯源报告 */
  generateTraceReport: (data: Record<string, unknown>) => {
    return envRequest.post('/trace/generateReport', { data });
  },
};

export default traceApi;
